"""
筛查策略核心数据结构

实现筛查策略的核心数据结构，包括策略配置、筛查间隔管理和验证功能。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Union
from datetime import datetime
from enum import Enum
import logging

from .enums import ScreeningToolType

logger = logging.getLogger(__name__)


class ScreeningFrequency(Enum):
    """筛查频率枚举"""
    ANNUAL = "annual"           # 年度筛查
    BIENNIAL = "biennial"       # 双年度筛查
    TRIENNIAL = "triennial"     # 三年一次
    CUSTOM = "custom"           # 自定义频率

    @property
    def years(self) -> float:
        """返回对应的年数"""
        frequency_map = {
            self.ANNUAL: 1.0,
            self.BIENNIAL: 2.0,
            self.TRIENNIAL: 3.0,
            self.CUSTOM: 0.0  # 自定义需要单独指定
        }
        return frequency_map[self]

    @property
    def display_name(self) -> str:
        """返回中文显示名称"""
        display_names = {
            self.ANNUAL: "年度筛查",
            self.BIENNIAL: "双年度筛查",
            self.TRIENNIAL: "三年一次",
            self.CUSTOM: "自定义频率"
        }
        return display_names[self]


@dataclass
class ScreeningInterval:
    """筛查间隔配置"""
    
    tool_type: ScreeningToolType
    start_age: int
    end_age: int
    frequency: ScreeningFrequency = ScreeningFrequency.ANNUAL
    custom_frequency_years: Optional[float] = None
    
    # 条件触发配置
    trigger_conditions: List[str] = field(default_factory=list)
    followup_actions: Dict[str, Any] = field(default_factory=dict)
    
    # 优先级和排序
    priority: int = 1
    sequence_order: int = 0
    
    def __post_init__(self):
        """验证筛查间隔配置"""
        self._validate_configuration()
    
    def _validate_configuration(self):
        """验证配置参数"""
        if self.start_age < 18 or self.start_age > 100:
            raise ValueError(f"开始年龄必须在18-100之间: {self.start_age}")
        
        if self.end_age < 18 or self.end_age > 100:
            raise ValueError(f"结束年龄必须在18-100之间: {self.end_age}")
        
        if self.start_age >= self.end_age:
            raise ValueError(f"开始年龄不能大于等于结束年龄: {self.start_age} >= {self.end_age}")
        
        if self.frequency == ScreeningFrequency.CUSTOM:
            if self.custom_frequency_years is None:
                raise ValueError("自定义频率必须指定custom_frequency_years")
            if self.custom_frequency_years <= 0 or self.custom_frequency_years > 20:
                raise ValueError(f"自定义频率必须在0-20年之间: {self.custom_frequency_years}")
    
    @property
    def frequency_years(self) -> float:
        """获取筛查频率（年）"""
        if self.frequency == ScreeningFrequency.CUSTOM:
            return self.custom_frequency_years or 1.0
        return self.frequency.years
    
    @property
    def age_range(self) -> tuple[int, int]:
        """获取年龄范围"""
        return (self.start_age, self.end_age)
    
    def overlaps_with(self, other: 'ScreeningInterval') -> bool:
        """检查是否与另一个间隔重叠"""
        if self.tool_type != other.tool_type:
            return False
        
        # 检查年龄范围重叠
        return not (self.end_age <= other.start_age or other.end_age <= self.start_age)
    
    def is_applicable_at_age(self, age: int) -> bool:
        """检查在指定年龄是否适用"""
        return self.start_age <= age <= self.end_age
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "tool_type": self.tool_type.value,
            "start_age": self.start_age,
            "end_age": self.end_age,
            "frequency": self.frequency.value,
            "custom_frequency_years": self.custom_frequency_years,
            "frequency_years": self.frequency_years,
            "trigger_conditions": self.trigger_conditions,
            "followup_actions": self.followup_actions,
            "priority": self.priority,
            "sequence_order": self.sequence_order
        }


@dataclass
class TargetPopulation:
    """目标人群配置"""
    
    age_range: tuple[int, int] = (50, 75)
    risk_level: str = "average"
    gender_restriction: Optional[str] = None
    exclusion_criteria: List[str] = field(default_factory=list)
    inclusion_criteria: List[str] = field(default_factory=list)
    
    # 人群特征
    population_size: Optional[int] = None
    geographic_region: Optional[str] = None
    socioeconomic_factors: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """验证目标人群配置"""
        # 移除强制验证，允许创建无效对象用于测试
        # 验证将在策略验证器中进行
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "age_range": list(self.age_range),  # 转换tuple为list以支持YAML序列化
            "risk_level": self.risk_level,
            "gender_restriction": self.gender_restriction,
            "exclusion_criteria": self.exclusion_criteria,
            "inclusion_criteria": self.inclusion_criteria,
            "population_size": self.population_size,
            "geographic_region": self.geographic_region,
            "socioeconomic_factors": self.socioeconomic_factors
        }


@dataclass
class ScreeningStrategy:
    """筛查策略核心类"""
    
    name: str
    description: str = ""
    intervals: List[ScreeningInterval] = field(default_factory=list)
    target_population: TargetPopulation = field(default_factory=TargetPopulation)
    
    # 元数据
    created_date: datetime = field(default_factory=datetime.now)
    modified_date: Optional[datetime] = None
    version: str = "1.0"
    author: str = ""
    tags: List[str] = field(default_factory=list)
    
    # 策略配置
    is_active: bool = True
    is_template: bool = False
    template_category: Optional[str] = None
    
    # 成本和预算约束
    budget_constraint: Optional[float] = None
    cost_effectiveness_threshold: Optional[float] = None
    
    def __post_init__(self):
        """初始化后验证"""
        # 移除强制验证，允许创建无效策略用于测试
        # 验证将在validate()方法中进行

        # 自动排序间隔
        if self.intervals:
            self.intervals.sort(key=lambda x: (x.sequence_order, x.start_age))
    
    def add_interval(self, interval: ScreeningInterval) -> None:
        """添加筛查间隔"""
        # 验证不与现有间隔冲突
        validation_errors = self._validate_new_interval(interval)
        if validation_errors:
            raise ValueError(f"间隔配置冲突: {'; '.join(validation_errors)}")
        
        self.intervals.append(interval)
        self.intervals.sort(key=lambda x: (x.sequence_order, x.start_age))
        self.modified_date = datetime.now()
    
    def remove_interval(self, interval: ScreeningInterval) -> bool:
        """移除筛查间隔"""
        try:
            self.intervals.remove(interval)
            self.modified_date = datetime.now()
            return True
        except ValueError:
            return False
    
    def get_intervals_for_age(self, age: int) -> List[ScreeningInterval]:
        """获取指定年龄适用的筛查间隔"""
        return [interval for interval in self.intervals 
                if interval.is_applicable_at_age(age)]
    
    def get_intervals_by_tool(self, tool_type: ScreeningToolType) -> List[ScreeningInterval]:
        """获取指定工具类型的筛查间隔"""
        return [interval for interval in self.intervals 
                if interval.tool_type == tool_type]
    
    def validate(self) -> List[str]:
        """验证策略配置，返回错误列表"""
        errors = []
        
        # 基本验证
        if not self.name.strip():
            errors.append("策略名称不能为空")
        
        if not self.intervals:
            errors.append("策略必须包含至少一个筛查间隔")
        
        # 验证每个间隔
        for i, interval in enumerate(self.intervals):
            try:
                interval._validate_configuration()
            except ValueError as e:
                errors.append(f"间隔{i+1}配置错误: {str(e)}")
        
        # 检查间隔重叠
        for i, interval1 in enumerate(self.intervals):
            for j, interval2 in enumerate(self.intervals[i+1:], i+1):
                if interval1.overlaps_with(interval2):
                    errors.append(f"间隔{i+1}与间隔{j+1}重叠: {interval1.tool_type.display_name}")
        
        # 验证目标人群
        try:
            self.target_population.__post_init__()
        except ValueError as e:
            errors.append(f"目标人群配置错误: {str(e)}")
        
        return errors
    
    def _validate_new_interval(self, new_interval: ScreeningInterval) -> List[str]:
        """验证新间隔是否与现有间隔冲突"""
        errors = []
        
        for existing_interval in self.intervals:
            if new_interval.overlaps_with(existing_interval):
                errors.append(f"与现有间隔重叠: {existing_interval.tool_type.display_name}")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "intervals": [interval.to_dict() for interval in self.intervals],
            "target_population": self.target_population.to_dict(),
            "created_date": self.created_date.isoformat(),
            "modified_date": self.modified_date.isoformat() if self.modified_date else None,
            "version": self.version,
            "author": self.author,
            "tags": self.tags,
            "is_active": self.is_active,
            "is_template": self.is_template,
            "template_category": self.template_category,
            "budget_constraint": self.budget_constraint,
            "cost_effectiveness_threshold": self.cost_effectiveness_threshold
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScreeningStrategy':
        """从字典创建策略实例"""
        # 解析间隔
        intervals = []
        for interval_data in data.get("intervals", []):
            tool_type = ScreeningToolType(interval_data["tool_type"])
            frequency = ScreeningFrequency(interval_data["frequency"])
            
            interval = ScreeningInterval(
                tool_type=tool_type,
                start_age=interval_data["start_age"],
                end_age=interval_data["end_age"],
                frequency=frequency,
                custom_frequency_years=interval_data.get("custom_frequency_years"),
                trigger_conditions=interval_data.get("trigger_conditions", []),
                followup_actions=interval_data.get("followup_actions", {}),
                priority=interval_data.get("priority", 1),
                sequence_order=interval_data.get("sequence_order", 0)
            )
            intervals.append(interval)
        
        # 解析目标人群
        pop_data = data.get("target_population", {})
        target_population = TargetPopulation(
            age_range=tuple(pop_data.get("age_range", (50, 75))),
            risk_level=pop_data.get("risk_level", "average"),
            gender_restriction=pop_data.get("gender_restriction"),
            exclusion_criteria=pop_data.get("exclusion_criteria", []),
            inclusion_criteria=pop_data.get("inclusion_criteria", []),
            population_size=pop_data.get("population_size"),
            geographic_region=pop_data.get("geographic_region"),
            socioeconomic_factors=pop_data.get("socioeconomic_factors", {})
        )
        
        # 创建策略实例
        strategy = cls(
            name=data["name"],
            description=data.get("description", ""),
            intervals=intervals,
            target_population=target_population,
            version=data.get("version", "1.0"),
            author=data.get("author", ""),
            tags=data.get("tags", []),
            is_active=data.get("is_active", True),
            is_template=data.get("is_template", False),
            template_category=data.get("template_category"),
            budget_constraint=data.get("budget_constraint"),
            cost_effectiveness_threshold=data.get("cost_effectiveness_threshold")
        )
        
        # 设置日期
        if "created_date" in data:
            strategy.created_date = datetime.fromisoformat(data["created_date"])
        if "modified_date" in data and data["modified_date"]:
            strategy.modified_date = datetime.fromisoformat(data["modified_date"])
        
        return strategy
