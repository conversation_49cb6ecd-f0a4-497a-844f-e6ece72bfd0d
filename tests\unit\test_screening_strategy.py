"""
筛查策略核心数据结构测试

测试筛查策略、筛查间隔和目标人群的创建、配置和验证功能。
"""

import pytest
from datetime import datetime
from unittest.mock import Mock

from src.modules.screening.strategy import (
    ScreeningStrategy, ScreeningInterval, TargetPopulation,
    ScreeningFrequency
)
from src.modules.screening.enums import ScreeningToolType


class TestScreeningFrequency:
    """测试筛查频率枚举"""
    
    def test_frequency_years(self):
        """测试频率年数映射"""
        assert ScreeningFrequency.ANNUAL.years == 1.0
        assert ScreeningFrequency.BIENNIAL.years == 2.0
        assert ScreeningFrequency.TRIENNIAL.years == 3.0
        assert ScreeningFrequency.CUSTOM.years == 0.0
    
    def test_display_names(self):
        """测试中文显示名称"""
        assert ScreeningFrequency.ANNUAL.display_name == "年度筛查"
        assert ScreeningFrequency.BIENNIAL.display_name == "双年度筛查"
        assert ScreeningFrequency.TRIENNIAL.display_name == "三年一次"
        assert ScreeningFrequency.CUSTOM.display_name == "自定义频率"


class TestScreeningInterval:
    """测试筛查间隔配置"""
    
    def test_valid_interval_creation(self):
        """测试有效间隔创建"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.ANNUAL
        )
        
        assert interval.tool_type == ScreeningToolType.FIT
        assert interval.start_age == 50
        assert interval.end_age == 75
        assert interval.frequency == ScreeningFrequency.ANNUAL
        assert interval.frequency_years == 1.0
        assert interval.age_range == (50, 75)
    
    def test_custom_frequency(self):
        """测试自定义频率"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.CUSTOM,
            custom_frequency_years=10.0
        )
        
        assert interval.frequency_years == 10.0
    
    def test_invalid_age_range(self):
        """测试无效年龄范围"""
        with pytest.raises(ValueError, match="开始年龄不能大于等于结束年龄"):
            ScreeningInterval(
                tool_type=ScreeningToolType.FIT,
                start_age=75,
                end_age=50
            )
    
    def test_invalid_start_age(self):
        """测试无效开始年龄"""
        with pytest.raises(ValueError, match="开始年龄必须在18-100之间"):
            ScreeningInterval(
                tool_type=ScreeningToolType.FIT,
                start_age=10,
                end_age=75
            )
    
    def test_invalid_end_age(self):
        """测试无效结束年龄"""
        with pytest.raises(ValueError, match="结束年龄必须在18-100之间"):
            ScreeningInterval(
                tool_type=ScreeningToolType.FIT,
                start_age=50,
                end_age=110
            )
    
    def test_custom_frequency_validation(self):
        """测试自定义频率验证"""
        with pytest.raises(ValueError, match="自定义频率必须指定custom_frequency_years"):
            ScreeningInterval(
                tool_type=ScreeningToolType.FIT,
                start_age=50,
                end_age=75,
                frequency=ScreeningFrequency.CUSTOM
            )
        
        with pytest.raises(ValueError, match="自定义频率必须在0-20年之间"):
            ScreeningInterval(
                tool_type=ScreeningToolType.FIT,
                start_age=50,
                end_age=75,
                frequency=ScreeningFrequency.CUSTOM,
                custom_frequency_years=25.0
            )
    
    def test_overlap_detection(self):
        """测试重叠检测"""
        interval1 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=70
        )
        
        interval2 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=60,
            end_age=80
        )
        
        interval3 = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=60,
            end_age=80
        )
        
        assert interval1.overlaps_with(interval2)
        assert not interval1.overlaps_with(interval3)  # 不同工具类型
    
    def test_age_applicability(self):
        """测试年龄适用性"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        assert interval.is_applicable_at_age(50)
        assert interval.is_applicable_at_age(60)
        assert interval.is_applicable_at_age(75)
        assert not interval.is_applicable_at_age(49)
        assert not interval.is_applicable_at_age(76)
    
    def test_to_dict(self):
        """测试字典转换"""
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.BIENNIAL,
            trigger_conditions=["negative_result"],
            priority=2
        )
        
        data = interval.to_dict()
        
        assert data["tool_type"] == "fecal_immunochemical_test"
        assert data["start_age"] == 50
        assert data["end_age"] == 75
        assert data["frequency"] == "biennial"
        assert data["frequency_years"] == 2.0
        assert data["trigger_conditions"] == ["negative_result"]
        assert data["priority"] == 2


class TestTargetPopulation:
    """测试目标人群配置"""
    
    def test_valid_population_creation(self):
        """测试有效人群创建"""
        population = TargetPopulation(
            age_range=(50, 75),
            risk_level="average",
            exclusion_criteria=["previous_cancer"]
        )
        
        assert population.age_range == (50, 75)
        assert population.risk_level == "average"
        assert population.exclusion_criteria == ["previous_cancer"]
    
    def test_invalid_age_range(self):
        """测试无效年龄范围"""
        with pytest.raises(ValueError, match="目标人群开始年龄不能大于等于结束年龄"):
            TargetPopulation(age_range=(75, 50))
    
    def test_to_dict(self):
        """测试字典转换"""
        population = TargetPopulation(
            age_range=(50, 75),
            risk_level="high",
            gender_restriction="female",
            population_size=100000
        )
        
        data = population.to_dict()
        
        assert data["age_range"] == (50, 75)
        assert data["risk_level"] == "high"
        assert data["gender_restriction"] == "female"
        assert data["population_size"] == 100000


class TestScreeningStrategy:
    """测试筛查策略"""
    
    def test_valid_strategy_creation(self):
        """测试有效策略创建"""
        strategy = ScreeningStrategy(
            name="测试策略",
            description="测试用筛查策略"
        )
        
        assert strategy.name == "测试策略"
        assert strategy.description == "测试用筛查策略"
        assert strategy.version == "1.0"
        assert strategy.is_active is True
        assert isinstance(strategy.created_date, datetime)
    
    def test_empty_name_validation(self):
        """测试空名称验证"""
        with pytest.raises(ValueError, match="策略名称不能为空"):
            ScreeningStrategy(name="")
    
    def test_add_interval(self):
        """测试添加间隔"""
        strategy = ScreeningStrategy(name="测试策略")
        
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        strategy.add_interval(interval)
        
        assert len(strategy.intervals) == 1
        assert strategy.intervals[0] == interval
        assert strategy.modified_date is not None
    
    def test_add_conflicting_interval(self):
        """测试添加冲突间隔"""
        strategy = ScreeningStrategy(name="测试策略")
        
        interval1 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=70
        )
        
        interval2 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=60,
            end_age=80
        )
        
        strategy.add_interval(interval1)
        
        with pytest.raises(ValueError, match="间隔配置冲突"):
            strategy.add_interval(interval2)
    
    def test_remove_interval(self):
        """测试移除间隔"""
        strategy = ScreeningStrategy(name="测试策略")
        
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        
        strategy.add_interval(interval)
        assert len(strategy.intervals) == 1
        
        result = strategy.remove_interval(interval)
        assert result is True
        assert len(strategy.intervals) == 0
    
    def test_get_intervals_for_age(self):
        """测试按年龄获取间隔"""
        strategy = ScreeningStrategy(name="测试策略")
        
        interval1 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=70
        )
        
        interval2 = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=60,
            end_age=80
        )
        
        strategy.add_interval(interval1)
        strategy.add_interval(interval2)
        
        intervals_at_55 = strategy.get_intervals_for_age(55)
        assert len(intervals_at_55) == 1
        assert intervals_at_55[0] == interval1
        
        intervals_at_65 = strategy.get_intervals_for_age(65)
        assert len(intervals_at_65) == 2
    
    def test_get_intervals_by_tool(self):
        """测试按工具类型获取间隔"""
        strategy = ScreeningStrategy(name="测试策略")
        
        interval1 = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=70
        )
        
        interval2 = ScreeningInterval(
            tool_type=ScreeningToolType.COLONOSCOPY,
            start_age=60,
            end_age=80
        )
        
        strategy.add_interval(interval1)
        strategy.add_interval(interval2)
        
        fit_intervals = strategy.get_intervals_by_tool(ScreeningToolType.FIT)
        assert len(fit_intervals) == 1
        assert fit_intervals[0] == interval1
    
    def test_validation(self):
        """测试策略验证"""
        strategy = ScreeningStrategy(name="测试策略")
        
        # 空策略应该有错误
        errors = strategy.validate()
        assert "策略必须包含至少一个筛查间隔" in errors
        
        # 添加有效间隔
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75
        )
        strategy.add_interval(interval)
        
        # 现在应该没有错误
        errors = strategy.validate()
        assert len(errors) == 0
    
    def test_to_dict_and_from_dict(self):
        """测试字典序列化和反序列化"""
        original_strategy = ScreeningStrategy(
            name="测试策略",
            description="测试描述",
            version="2.0",
            author="测试作者"
        )
        
        interval = ScreeningInterval(
            tool_type=ScreeningToolType.FIT,
            start_age=50,
            end_age=75,
            frequency=ScreeningFrequency.BIENNIAL
        )
        original_strategy.add_interval(interval)
        
        # 转换为字典
        data = original_strategy.to_dict()
        
        # 从字典重建
        restored_strategy = ScreeningStrategy.from_dict(data)
        
        assert restored_strategy.name == original_strategy.name
        assert restored_strategy.description == original_strategy.description
        assert restored_strategy.version == original_strategy.version
        assert restored_strategy.author == original_strategy.author
        assert len(restored_strategy.intervals) == 1
        
        restored_interval = restored_strategy.intervals[0]
        assert restored_interval.tool_type == interval.tool_type
        assert restored_interval.start_age == interval.start_age
        assert restored_interval.end_age == interval.end_age
        assert restored_interval.frequency == interval.frequency
