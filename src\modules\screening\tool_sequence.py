"""
顺序筛查工具管理

实现筛查工具序列的调度、执行和状态跟踪功能。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Callable
from datetime import datetime, timedelta
from enum import Enum
import logging
import heapq

from src.core.individual import Individual
from .enums import ScreeningToolType, ScreeningResult
from .strategy import ScreeningStrategy, ScreeningInterval
from .screening_tool import ScreeningToolFactory

logger = logging.getLogger(__name__)


class SchedulingStatus(Enum):
    """调度状态枚举"""
    PENDING = "pending"           # 待执行
    SCHEDULED = "scheduled"       # 已调度
    EXECUTING = "executing"       # 执行中
    COMPLETED = "completed"       # 已完成
    CANCELLED = "cancelled"       # 已取消
    FAILED = "failed"            # 执行失败


class TriggerCondition(Enum):
    """触发条件枚举"""
    NEGATIVE_RESULT = "negative_result"     # 阴性结果
    POSITIVE_RESULT = "positive_result"     # 阳性结果
    HIGH_RISK_FACTORS = "high_risk_factors" # 高风险因素
    AGE_THRESHOLD = "age_threshold"         # 年龄阈值
    TIME_INTERVAL = "time_interval"         # 时间间隔
    MANUAL_TRIGGER = "manual_trigger"       # 手动触发


@dataclass
class ScheduledScreening:
    """计划筛查事件"""
    
    individual_id: str
    tool_type: ScreeningToolType
    scheduled_time: float  # 模拟时间
    interval_config: ScreeningInterval
    
    # 调度信息
    scheduling_id: str = field(default_factory=lambda: f"sched_{datetime.now().timestamp()}")
    status: SchedulingStatus = SchedulingStatus.PENDING
    priority: int = 1
    
    # 触发条件
    trigger_condition: Optional[TriggerCondition] = None
    trigger_data: Dict[str, Any] = field(default_factory=dict)
    
    # 执行结果
    execution_time: Optional[float] = None
    result: Optional[Any] = None
    error_message: Optional[str] = None
    
    # 后续行动
    followup_actions: List[str] = field(default_factory=list)
    
    def __lt__(self, other):
        """支持优先队列排序"""
        if self.scheduled_time != other.scheduled_time:
            return self.scheduled_time < other.scheduled_time
        return self.priority < other.priority
    
    def is_due(self, current_time: float) -> bool:
        """检查是否到期"""
        return current_time >= self.scheduled_time
    
    def can_execute(self, current_time: float) -> bool:
        """检查是否可以执行"""
        return (self.status == SchedulingStatus.SCHEDULED and 
                self.is_due(current_time))
    
    def mark_completed(self, result: Any, execution_time: float):
        """标记为完成"""
        self.status = SchedulingStatus.COMPLETED
        self.result = result
        self.execution_time = execution_time
    
    def mark_failed(self, error_message: str, execution_time: float):
        """标记为失败"""
        self.status = SchedulingStatus.FAILED
        self.error_message = error_message
        self.execution_time = execution_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "scheduling_id": self.scheduling_id,
            "individual_id": self.individual_id,
            "tool_type": self.tool_type.value,
            "scheduled_time": self.scheduled_time,
            "status": self.status.value,
            "priority": self.priority,
            "trigger_condition": self.trigger_condition.value if self.trigger_condition else None,
            "trigger_data": self.trigger_data,
            "execution_time": self.execution_time,
            "result": self.result,
            "error_message": self.error_message,
            "followup_actions": self.followup_actions
        }


@dataclass
class SequenceConstraint:
    """序列约束配置"""
    
    predecessor_tool: ScreeningToolType
    successor_tool: ScreeningToolType
    min_time_gap: float = 0.0  # 最小时间间隔（年）
    max_time_gap: Optional[float] = None  # 最大时间间隔（年）
    
    # 条件约束
    required_result: Optional[ScreeningResult] = None
    required_conditions: List[str] = field(default_factory=list)
    
    def validate_timing(self, predecessor_time: float, successor_time: float) -> bool:
        """验证时间约束"""
        time_gap = successor_time - predecessor_time
        
        if time_gap < self.min_time_gap:
            return False
        
        if self.max_time_gap is not None and time_gap > self.max_time_gap:
            return False
        
        return True
    
    def validate_conditions(self, predecessor_result: Any, context: Dict[str, Any]) -> bool:
        """验证条件约束"""
        if self.required_result is not None:
            if not hasattr(predecessor_result, 'result_type'):
                return False
            if predecessor_result.result_type != self.required_result:
                return False
        
        for condition in self.required_conditions:
            if condition not in context or not context[condition]:
                return False
        
        return True


class ToolSequence:
    """工具序列管理器"""
    
    def __init__(self, strategy: ScreeningStrategy):
        """
        初始化工具序列管理器
        
        Args:
            strategy: 筛查策略配置
        """
        self.strategy = strategy
        self.execution_queue: List[ScheduledScreening] = []
        self.completed_screenings: List[ScheduledScreening] = []
        self.constraints: List[SequenceConstraint] = []
        
        # 状态跟踪
        self.individual_histories: Dict[str, List[ScheduledScreening]] = {}
        self.execution_statistics: Dict[str, Any] = {
            "total_scheduled": 0,
            "total_completed": 0,
            "total_failed": 0,
            "total_cancelled": 0
        }
        
        # 回调函数
        self.on_screening_completed: Optional[Callable] = None
        self.on_screening_failed: Optional[Callable] = None
        
        # 初始化默认约束
        self._setup_default_constraints()
    
    def _setup_default_constraints(self):
        """设置默认序列约束"""
        # FIT阳性后结肠镜检查约束
        fit_to_colonoscopy = SequenceConstraint(
            predecessor_tool=ScreeningToolType.FIT,
            successor_tool=ScreeningToolType.COLONOSCOPY,
            min_time_gap=0.0,  # 立即执行
            max_time_gap=0.5,  # 6个月内
            required_result=ScreeningResult.POSITIVE
        )
        self.constraints.append(fit_to_colonoscopy)
        
        # 乙状结肠镜后结肠镜检查约束
        sigmoid_to_colonoscopy = SequenceConstraint(
            predecessor_tool=ScreeningToolType.SIGMOIDOSCOPY,
            successor_tool=ScreeningToolType.COLONOSCOPY,
            min_time_gap=0.0,
            max_time_gap=1.0,  # 1年内
            required_result=ScreeningResult.POSITIVE
        )
        self.constraints.append(sigmoid_to_colonoscopy)
    
    def add_constraint(self, constraint: SequenceConstraint):
        """添加序列约束"""
        self.constraints.append(constraint)
    
    def schedule_screening(
        self,
        individual: Individual,
        tool_type: ScreeningToolType,
        scheduled_time: float,
        interval_config: ScreeningInterval,
        trigger_condition: Optional[TriggerCondition] = None,
        trigger_data: Optional[Dict[str, Any]] = None,
        priority: int = 1
    ) -> ScheduledScreening:
        """
        安排筛查
        
        Args:
            individual: 个体对象
            tool_type: 筛查工具类型
            scheduled_time: 计划执行时间
            interval_config: 间隔配置
            trigger_condition: 触发条件
            trigger_data: 触发数据
            priority: 优先级
            
        Returns:
            计划筛查对象
        """
        scheduled_screening = ScheduledScreening(
            individual_id=individual.individual_id,
            tool_type=tool_type,
            scheduled_time=scheduled_time,
            interval_config=interval_config,
            status=SchedulingStatus.SCHEDULED,
            priority=priority,
            trigger_condition=trigger_condition,
            trigger_data=trigger_data or {}
        )
        
        # 验证序列约束
        if not self._validate_sequence_constraints(individual, scheduled_screening):
            logger.warning(f"序列约束验证失败: {individual.individual_id}, {tool_type}")
            scheduled_screening.status = SchedulingStatus.CANCELLED
            return scheduled_screening
        
        # 添加到执行队列
        heapq.heappush(self.execution_queue, scheduled_screening)
        
        # 更新个体历史
        if individual.individual_id not in self.individual_histories:
            self.individual_histories[individual.individual_id] = []
        self.individual_histories[individual.individual_id].append(scheduled_screening)
        
        # 更新统计
        self.execution_statistics["total_scheduled"] += 1
        
        logger.info(f"已安排筛查: {individual.individual_id}, {tool_type}, 时间: {scheduled_time}")
        
        return scheduled_screening
    
    def schedule_next_screening(self, individual: Individual, current_time: float):
        """为个体安排下一次筛查"""
        eligible_intervals = self._get_eligible_intervals(individual, current_time)
        
        for interval in eligible_intervals:
            next_screening_time = self._calculate_next_screening_time(
                individual, interval, current_time
            )
            
            if next_screening_time:
                self.schedule_screening(
                    individual=individual,
                    tool_type=interval.tool_type,
                    scheduled_time=next_screening_time,
                    interval_config=interval,
                    trigger_condition=TriggerCondition.TIME_INTERVAL
                )
    
    def execute_due_screenings(self, current_time: float) -> List[ScheduledScreening]:
        """执行到期的筛查"""
        executed_screenings = []
        
        while self.execution_queue and self.execution_queue[0].can_execute(current_time):
            scheduled_screening = heapq.heappop(self.execution_queue)
            
            try:
                # 执行筛查
                result = self._execute_screening(scheduled_screening, current_time)
                scheduled_screening.mark_completed(result, current_time)
                
                # 触发后续行动
                self._trigger_followup_actions(scheduled_screening, result)
                
                # 更新统计
                self.execution_statistics["total_completed"] += 1
                
                # 调用回调
                if self.on_screening_completed:
                    self.on_screening_completed(scheduled_screening, result)
                
                logger.info(f"筛查完成: {scheduled_screening.individual_id}, {scheduled_screening.tool_type}")
                
            except Exception as e:
                scheduled_screening.mark_failed(str(e), current_time)
                self.execution_statistics["total_failed"] += 1
                
                if self.on_screening_failed:
                    self.on_screening_failed(scheduled_screening, e)
                
                logger.error(f"筛查失败: {scheduled_screening.individual_id}, {scheduled_screening.tool_type}, 错误: {e}")
            
            self.completed_screenings.append(scheduled_screening)
            executed_screenings.append(scheduled_screening)
        
        return executed_screenings
    
    def _validate_sequence_constraints(
        self,
        individual: Individual,
        scheduled_screening: ScheduledScreening
    ) -> bool:
        """验证序列约束"""
        individual_history = self.individual_histories.get(individual.individual_id, [])
        
        for constraint in self.constraints:
            if constraint.successor_tool != scheduled_screening.tool_type:
                continue
            
            # 查找前置工具的执行记录
            predecessor_screenings = [
                s for s in individual_history
                if (s.tool_type == constraint.predecessor_tool and
                    s.status == SchedulingStatus.COMPLETED)
            ]
            
            if not predecessor_screenings:
                continue  # 没有前置工具记录，跳过约束检查
            
            # 获取最近的前置工具执行记录
            latest_predecessor = max(predecessor_screenings, key=lambda s: s.execution_time or 0)
            
            # 验证时间约束
            if not constraint.validate_timing(
                latest_predecessor.execution_time or 0,
                scheduled_screening.scheduled_time
            ):
                return False
            
            # 验证条件约束
            context = {
                "individual": individual,
                "predecessor_result": latest_predecessor.result
            }
            if not constraint.validate_conditions(latest_predecessor.result, context):
                return False
        
        return True
    
    def _get_eligible_intervals(self, individual: Individual, current_time: float) -> List[ScreeningInterval]:
        """获取个体在当前时间的合适筛查间隔"""
        current_age = individual.get_current_age()
        return [
            interval for interval in self.strategy.intervals
            if interval.is_applicable_at_age(current_age)
        ]
    
    def _calculate_next_screening_time(
        self,
        individual: Individual,
        interval: ScreeningInterval,
        current_time: float
    ) -> Optional[float]:
        """计算下一次筛查时间"""
        # 获取个体的筛查历史
        individual_history = self.individual_histories.get(individual.individual_id, [])
        
        # 查找该工具类型的最近筛查
        tool_screenings = [
            s for s in individual_history
            if (s.tool_type == interval.tool_type and
                s.status == SchedulingStatus.COMPLETED)
        ]
        
        if tool_screenings:
            # 基于最近筛查计算下一次时间
            latest_screening = max(tool_screenings, key=lambda s: s.execution_time or 0)
            next_time = (latest_screening.execution_time or 0) + interval.frequency_years
        else:
            # 首次筛查，基于当前时间计算
            next_time = current_time + interval.frequency_years
        
        # 检查是否在年龄范围内
        future_age = individual.get_current_age() + (next_time - current_time)
        if not interval.is_applicable_at_age(int(future_age)):
            return None
        
        return next_time
    
    def _execute_screening(self, scheduled_screening: ScheduledScreening, current_time: float):
        """执行筛查"""
        # 创建筛查工具
        tool = ScreeningToolFactory.create_tool(scheduled_screening.tool_type)
        
        # 获取个体对象（这里需要从某个地方获取，简化处理）
        # 在实际实现中，可能需要从人群管理器或数据库中获取
        individual = self._get_individual(scheduled_screening.individual_id)
        
        # 执行筛查
        result = tool.perform_screening(individual)
        
        return result
    
    def _get_individual(self, individual_id: str) -> Individual:
        """获取个体对象（占位符方法）"""
        # 这里应该从人群管理器或数据库中获取个体
        # 为了测试目的，返回一个模拟对象
        from src.core.enums import Gender, DiseaseState
        individual = Individual(
            birth_year=1970,
            gender=Gender.MALE,
            individual_id=individual_id
        )
        return individual
    
    def _trigger_followup_actions(self, scheduled_screening: ScheduledScreening, result):
        """触发后续行动"""
        if not hasattr(result, 'result_type'):
            return
        
        # 根据结果类型触发相应行动
        if result.result_type == ScreeningResult.POSITIVE:
            self._handle_positive_result(scheduled_screening, result)
        elif result.result_type == ScreeningResult.NEGATIVE:
            self._handle_negative_result(scheduled_screening, result)
    
    def _handle_positive_result(self, scheduled_screening: ScheduledScreening, result):
        """处理阳性结果"""
        # 根据工具类型触发相应的后续筛查
        if scheduled_screening.tool_type == ScreeningToolType.FIT:
            # FIT阳性，安排结肠镜检查
            individual = self._get_individual(scheduled_screening.individual_id)
            colonoscopy_interval = ScreeningInterval(
                tool_type=ScreeningToolType.COLONOSCOPY,
                start_age=18,
                end_age=100
            )
            
            self.schedule_screening(
                individual=individual,
                tool_type=ScreeningToolType.COLONOSCOPY,
                scheduled_time=scheduled_screening.execution_time + 0.1,  # 1个月后
                interval_config=colonoscopy_interval,
                trigger_condition=TriggerCondition.POSITIVE_RESULT,
                priority=0  # 高优先级
            )
    
    def _handle_negative_result(self, scheduled_screening: ScheduledScreening, result):
        """处理阴性结果"""
        # 继续常规筛查计划
        pass
    
    def get_individual_history(self, individual_id: str) -> List[ScheduledScreening]:
        """获取个体筛查历史"""
        return self.individual_histories.get(individual_id, [])
    
    def get_pending_screenings(self) -> List[ScheduledScreening]:
        """获取待执行的筛查"""
        return [s for s in self.execution_queue if s.status == SchedulingStatus.SCHEDULED]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取执行统计"""
        return self.execution_statistics.copy()
    
    def cancel_screening(self, scheduling_id: str) -> bool:
        """取消筛查"""
        for scheduled_screening in self.execution_queue:
            if scheduled_screening.scheduling_id == scheduling_id:
                scheduled_screening.status = SchedulingStatus.CANCELLED
                self.execution_statistics["total_cancelled"] += 1
                return True
        return False
